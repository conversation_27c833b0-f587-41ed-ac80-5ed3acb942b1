#!/usr/bin/env python3
"""
8 Şekli Çizme Görevi - V-tail Uçak için
Bu script MAVProxy'yi baş<PERSON><PERSON>r, uçağı uçurur ve belirlenen direkler etrafında 8 şekli çizdirir.
"""
import os
import sys
import time
import math
import argparse
import subprocess
from pymavlink import mavutil
from dronekit import connect, VehicleMode, LocationGlobalRelative, Command, LocationGlobal
from pymavlink.mavutil import mavlink

# Argüman parser'ı oluştur
parser = argparse.ArgumentParser(description='8 şekli çizme görevi')
parser.add_argument('--connect', default='udp:127.0.0.1:14550', help='Bağlantı dizesi')
parser.add_argument('--mavproxy', action='store_true', help='MAVProxy başlat')
parser.add_argument('--altitude', type=float, default=50.0, help='Uçuş yüksekliği (metre)')
parser.add_argument('--radius', type=float, default=30.0, help='<PERSON><PERSON>nüş yarıçapı (metre)')
args = parser.parse_args()

# MAVProxy'yi başlat (eğer istenirse)
mavproxy_process = None
if args.mavproxy:
    print("MAVProxy başlatılıyor...")
    cmd = ['python', '-c', 'from MAVProxy.mavproxy import main; main()', '--master=' + args.connect, '--console', '--map']
    mavproxy_process = subprocess.Popen(cmd)
    time.sleep(5)  # MAVProxy'nin başlaması için bekle

# Araca bağlan
print(f"Araca bağlanılıyor: {args.connect}")
vehicle = connect(args.connect, wait_ready=True)

def get_distance_metres(aLocation1, aLocation2):
    """İki konum arasındaki mesafeyi metre cinsinden hesaplar"""
    dlat = aLocation2.lat - aLocation1.lat
    dlong = aLocation2.lon - aLocation1.lon
    return math.sqrt((dlat*dlat) + (dlong*dlong)) * 1.113195e5

def create_eight_pattern(center_lat, center_lon, radius, altitude):
    """İki direk etrafında 8 şekli oluşturan waypoint'leri hesaplar"""
    waypoints = []
    
    # İlk direk etrafında daire (saat yönünde)
    for i in range(8):
        angle = math.radians(i * 45)
        lat = center_lat + (radius * math.cos(angle) * 1e-5)
        lon = center_lon + (radius * math.sin(angle) * 1e-5)
        waypoints.append(LocationGlobalRelative(lat, lon, altitude))
    
    # İkinci direk etrafında daire (saat yönünün tersine)
    for i in range(8):
        angle = math.radians(180 + i * 45)
        lat = center_lat + (radius * math.cos(angle) * 1e-5)
        lon = center_lon + (radius * math.sin(angle) * 1e-5)
        waypoints.append(LocationGlobalRelative(lat, lon, altitude))
    
    return waypoints

def upload_mission(waypoints, home_location):
    """Waypoint'leri araca yükler"""
    cmds = vehicle.commands
    cmds.clear()
    
    # Takeoff komutu ekle
    cmds.add(Command(0, 0, 0, mavlink.MAV_FRAME_GLOBAL_RELATIVE_ALT, mavlink.MAV_CMD_NAV_TAKEOFF, 
                    0, 0, 0, 0, 0, 0, 0, 0, args.altitude))
    
    # Waypoint'leri ekle
    for i, waypoint in enumerate(waypoints):
        cmds.add(Command(0, 0, 0, mavlink.MAV_FRAME_GLOBAL_RELATIVE_ALT, mavlink.MAV_CMD_NAV_WAYPOINT, 
                        0, 0, 0, 0, 0, 0, waypoint.lat, waypoint.lon, waypoint.alt))
    
    # RTL (Return To Launch) komutu ekle
    cmds.add(Command(0, 0, 0, mavlink.MAV_FRAME_GLOBAL_RELATIVE_ALT, mavlink.MAV_CMD_NAV_RETURN_TO_LAUNCH, 
                    0, 0, 0, 0, 0, 0, 0, 0, 0))
    
    # Komutları yükle
    cmds.upload()
    print("Görev yüklendi.")

def arm_and_takeoff(target_altitude):
    """Aracı arm eder ve kalkış yapar"""
    print("Temel kontroller yapılıyor...")
    
    # Arm edilebilir durumda olana kadar bekle
    while not vehicle.is_armable:
        print("Arm için bekleniyor...")
        time.sleep(1)
    
    print("Arm ediliyor...")
    vehicle.mode = VehicleMode("GUIDED")
    vehicle.armed = True
    
    # Arm olana kadar bekle
    while not vehicle.armed:
        print("Arm olması bekleniyor...")
        time.sleep(1)
    
    print("Kalkış yapılıyor...")
    vehicle.simple_takeoff(target_altitude)
    
    # Hedef yüksekliğe ulaşana kadar bekle
    while True:
        current_altitude = vehicle.location.global_relative_frame.alt
        print(f"Yükseklik: {current_altitude}")
        if current_altitude >= target_altitude * 0.95:
            print("Hedef yüksekliğe ulaşıldı!")
            break
        time.sleep(1)

def main():
    try:
        # Direk konumlarını sahada gir
        print("\n--- 8 Şekli Çizme Görevi ---")
        print("Direk konumlarını girin:")
        
        pole1_lat = float(input("1. Direğin enlem değeri: "))
        pole1_lon = float(input("1. Direğin boylam değeri: "))
        pole2_lat = float(input("2. Direğin enlem değeri: "))
        pole2_lon = float(input("2. Direğin boylam değeri: "))
        
        # Uçuş parametreleri
        altitude = args.altitude  # metre
        radius = args.radius  # metre
        
        # Ev konumunu al
        home_location = vehicle.location.global_frame
        
        # Her direk için 8 şekli oluştur
        print("8 şekli rotası oluşturuluyor...")
        waypoints1 = create_eight_pattern(pole1_lat, pole1_lon, radius, altitude)
        waypoints2 = create_eight_pattern(pole2_lat, pole2_lon, radius, altitude)
        
        # Tüm waypoint'leri birleştir
        all_waypoints = waypoints1 + waypoints2
        
        # Görevi yükle
        print("Görev yükleniyor...")
        upload_mission(all_waypoints, home_location)
        
        # Kalkış yap
        print("Kalkış yapılıyor...")
        arm_and_takeoff(altitude)
        
        # Görevi başlat
        print("Görev başlatılıyor - 8 şekli çiziliyor...")
        vehicle.mode = VehicleMode("AUTO")
        
        # Görev tamamlanana kadar bekle
        while True:
            nextwaypoint = vehicle.commands.next
            print(f"Sonraki waypoint: {nextwaypoint}")
            
            # Görev tamamlandı mı kontrol et
            if nextwaypoint == len(all_waypoints) + 2:  # +2: takeoff ve RTL komutları
                print("Görev tamamlandı!")
                break
                
            time.sleep(2)
        
        # RTL moduna geç
        print("Eve dönülüyor...")
        vehicle.mode = VehicleMode("RTL")
        
        # İniş tamamlanana kadar bekle
        while vehicle.armed:
            print("İniş bekleniyor...")
            time.sleep(2)
            
        print("İniş tamamlandı!")
        
    except KeyboardInterrupt:
        print("\nKullanıcı tarafından durduruldu.")
    finally:
        # Bağlantıyı kapat
        print("Bağlantı kapatılıyor...")
        vehicle.close()
        
        # MAVProxy'yi kapat
        if mavproxy_process:
            print("MAVProxy kapatılıyor...")
            mavproxy_process.terminate()
        
        print("Program sonlandırıldı.")

if __name__ == "__main__":
    main()